import { PlusOutlined } from '@ant-design/icons';
import { Button, Table } from 'antd';
import { TableProps } from 'antd/lib';
import React, { useEffect, useMemo, useState } from 'react';
import { ActionsColumns } from '../../../../components/table/components/ActionsColumns';
import { THandover } from '../../../../types/deliveryConfig';
import { useStoreDeliveryConfig } from '../../storeDeliveryConfig';
import { v4 as uuidv4 } from 'uuid';
import ModalHandover from './ModalHandover';
import './style.scss';

const HandoverCategory = () => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [formatHandovers, setFormatHandovers] = useState<THandover[]>([]);
  const [openModalHandover, setOpenModalHandover] = useState(false);
  const [defaultCategory, setDefaultCategory] = useState<THandover>();
  const { dataHandovers, setDataHandovers } = useStoreDeliveryConfig();

  const columnActions: TableProps['columns'] = useMemo(
    () => [
      {
        title: 'STT',
        width: '5%',
        key: 'index',
        render: (_, __, index) => index + 1,
      },
      {
        title: 'Hạng mục',
        dataIndex: 'name',
        width: '25%',
        key: 'name',
        render: value => value ?? value,
      },
      {
        title: 'Loại hạng mục',
        width: '20%',
        dataIndex: 'type',
        key: 'type',
        render: (value, record: THandover) => {
          return record?.children ? '' : value;
        },
      },
      {
        title: 'Mô tả chi tiết',
        width: '35%',
        dataIndex: 'description',
        key: 'description',
        render: (value: string, record: THandover) => {
          if (record?.children) {
            return null;
          }
          return <div>{value ?? ''}</div>;
        },
      },
      {
        key: 'action',
        width: '5%',
        align: 'center',
        render: (_, record: THandover) => {
          if (!record?.children) {
            return null;
          }
          const handleModify = () => {
            setOpenModalHandover(true);
            setDefaultCategory(record);
          };
          const categoryDelete = () => {
            const newData = dataHandovers?.filter(item => item.id !== record.id);
            setDataHandovers(newData || []);
          };

          return (
            <ActionsColumns
              overlayClassName="action-column-data-role"
              moreActions={[
                {
                  label: 'Chỉnh sửa',
                  key: 'modify',
                  onClick: handleModify,
                },
                {
                  label: 'Xóa',
                  key: 'delete',
                  onClick: categoryDelete,
                },
              ]}
            />
          );
        },
      },
    ],
    [dataHandovers, setDataHandovers],
  );

  const handleOpenModal = () => {
    setOpenModalHandover(true);
  };
  const handleCloseModal = () => {
    setOpenModalHandover(false);
    setDefaultCategory(undefined);
  };

  useEffect(() => {
    if (dataHandovers) {
      setFormatHandovers(
        dataHandovers?.map(item => ({
          ...item,
          children: item?.list?.map(childItem => ({
            ...childItem,
            id: uuidv4(),
          })),
        })),
      );
    }
  }, [dataHandovers]);

  useEffect(() => {
    if (dataHandovers?.length) {
      const newExpandedKeys = dataHandovers?.map(item => item.id)?.filter(id => id !== undefined) as React.Key[];
      setExpandedKeys(newExpandedKeys);
    }
  }, [dataHandovers]);

  return (
    <>
      <h3>Hạng mục bàn giao</h3>
      <Button type="dashed" icon={<PlusOutlined />} onClick={handleOpenModal} style={{ marginBottom: 16 }}>
        Thêm hạng mục
      </Button>

      <Table
        className="handover-table"
        columns={columnActions}
        dataSource={formatHandovers}
        pagination={false}
        rowKey="id"
        expandable={{
          expandedRowKeys: expandedKeys,
          expandIcon: () => null,
        }}
      />
      <ModalHandover open={openModalHandover} onCancel={handleCloseModal} defaultValue={defaultCategory} />
    </>
  );
};

export default HandoverCategory;
