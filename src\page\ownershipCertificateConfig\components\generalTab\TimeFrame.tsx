import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Row, Select, TimePicker } from 'antd';
import Input from 'antd/es/input/Input';
import { DAY_ORDER_MAP, OBJECT_ALL_WEEK } from '../../../../constants/common';
import { TWeek } from '../../../../types/common/common';
import { handleKeyDownEnterNumber } from '../../../../utilities/regex';

const { Item, List } = Form;
const { RangePicker: TimeRangePicker } = TimePicker;

const TimeFrame = () => {
  const form = Form.useFormInstance();

  const handleDayChange = (value: string, name: number) => {
    const currentTimeFrames = form.getFieldValue('timeFrames') || [];

    currentTimeFrames[name] = {
      ...currentTimeFrames[name],
      day: value,
    };

    // Sắp xếp mảng theo thứ tự các ngày trong tuần
    const sortedTimeFrames = [...currentTimeFrames].sort((a: { day: TWeek }, b: { day: TWeek }) => {
      if (!a.day) return 1;
      if (!b.day) return -1;
      return DAY_ORDER_MAP[a.day] - DAY_ORDER_MAP[b.day];
    });

    // Cập nhật lại form với mảng đã sắp xếp
    form.setFieldsValue({ timeFrames: sortedTimeFrames });
  };

  const handleTimeRangeChange = (timeStrings: string[], name: number) => {
    const currentTimeFrames = form.getFieldValue('timeFrames') || [];
    // Cập nhật giá trị thời gian mới
    currentTimeFrames[name] = {
      ...currentTimeFrames[name],
      startTime: timeStrings[0],
      endTime: timeStrings[1],
    };
    form.setFieldsValue({ timeFrames: currentTimeFrames });
  };

  return (
    <>
      <Item label="Khung giờ bàn giao" required layout="horizontal" />
      <List
        name="timeFrames"
        initialValue={[{}]}
        rules={[
          {
            validator: async (_, timeFrames) => {
              if (!timeFrames || timeFrames.length === 0) {
                return Promise.reject(new Error('Vui lòng nhập khung giờ bàn giao'));
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        {(fields, { add, remove }, { errors }) => (
          <>
            <Button onClick={() => add()} icon={<PlusOutlined />} style={{ marginBottom: 16 }}>
              Thêm khung giờ
            </Button>
            {fields.map(({ key, name, ...restField }) => {
              const timeFrames = form.getFieldValue(['timeFrames']) || [];
              const currentTimeFrame = timeFrames[name];

              const isDuplicate = timeFrames.some(
                (timeFrame: { startTime: string; endTime: string; day: string }, index: number) =>
                  index !== name &&
                  timeFrame?.day === currentTimeFrame?.day &&
                  timeFrame?.startTime === currentTimeFrame?.startTime &&
                  timeFrame?.endTime === currentTimeFrame?.endTime,
              );
              return (
                <Row key={key} gutter={16} className="row-time-frame">
                  <Col span={6}>
                    <Item
                      {...restField}
                      rules={[{ required: true, message: 'Vui lòng chọn thứ' }]}
                      name={[name, 'day']}
                    >
                      <Select
                        placeholder="Thứ mấy?"
                        options={OBJECT_ALL_WEEK}
                        onChange={value => handleDayChange(value, name)}
                      />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      {...restField}
                      name={[name, 'timeRange']}
                      help={isDuplicate ? 'Khung giờ bàn giao bị trùng' : undefined}
                      validateStatus={isDuplicate ? 'error' : undefined}
                      rules={[{ required: true, message: 'Vui lòng chọn khung giờ' }]}
                    >
                      <TimeRangePicker
                        format="HH:mm"
                        placeholder={['Giờ bắt đầu', 'Giờ kết thúc']}
                        onChange={(_, timeStrings) => handleTimeRangeChange(timeStrings, name)}
                      />
                    </Item>
                  </Col>
                  <Col span={8}>
                    <Item
                      {...restField}
                      name={[name, 'amount']}
                      rules={[{ required: true, message: 'Vui lòng nhập số lượng' }]}
                    >
                      <Input placeholder="Số lượng cân" maxLength={13} onKeyDown={handleKeyDownEnterNumber} />
                    </Item>
                  </Col>
                  <Col span={2}>
                    <DeleteOutlined onClick={() => remove(name)} style={{ color: 'red' }} />
                  </Col>
                </Row>
              );
            })}
            {errors.length > 0 && (
              <span style={{ color: 'red' }}>
                <Form.ErrorList errors={errors} />
              </span>
            )}
          </>
        )}
      </List>
    </>
  );
};

export default TimeFrame;
