@import url('https://fonts.googleapis.com/css2?family=Roboto&family=Montserrat&family=Open+Sans&family=Ubuntu&display=swap');

/* Phần tử căn chỉnh */
.row-time-frame {
  align-items: baseline;
}

.ql-toolbar.ql-snow .ql-picker-label {
  overflow: clip;
}
/* Font styles */
.ql-font-sans-serif {
  font-family: sans-serif;
}
.ql-font-serif {
  font-family: serif;
}
.ql-font-monospace {
  font-family: monospace;
}
.ql-font-arial {
  font-family: Arial, sans-serif;
}
.ql-font-roboto {
  font-family: 'Roboto', sans-serif;
}
.ql-font-open-sans {
  font-family: 'Open Sans', sans-serif;
}
.ql-font-montserrat {
  font-family: 'Montserrat', sans-serif;
}
.ql-font-inconsolata {
  font-family: 'Inconsolata', monospace;
}
.ql-font-ubuntu {
  font-family: 'Ubuntu', sans-serif;
}
.ql-font-times-new-roman {
  font-family: 'Times New Roman', Times, serif;
}

/* Label & item hiển thị tên font trong dropdown */
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='sans-serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='sans-serif']::before {
  content: 'Sans Serif';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before {
  content: 'Serif';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before {
  content: 'Monospace';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='arial']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='arial']::before {
  content: 'Arial';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='roboto']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='roboto']::before {
  content: 'Roboto';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='open-sans']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='open-sans']::before {
  content: 'Open Sans';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='montserrat']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='montserrat']::before {
  content: 'Montserrat';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='inconsolata']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='inconsolata']::before {
  content: 'Inconsolata';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='ubuntu']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='ubuntu']::before {
  content: 'Ubuntu';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='times-new-roman']::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='times-new-roman']::before {
  content: 'Times New Roman';
}
