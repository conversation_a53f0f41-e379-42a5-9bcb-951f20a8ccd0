import { Button, Flex, Modal, notification, TableColumnsType, Tag } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import { Key, useEffect, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { useDeleteField, useFetch, useUpdateField } from '../../../hooks';
import FilterSearch from './components/filterSearch';
import './styles.scss';
import ModalAssignDebtReport from './components/modalManualDeliver/ModalManualDeliver';
import React from 'react';
import { columns } from './columns';
import { IDebReport } from '../../../types/debtReport';
import { deleteAssigner, exportDebtReport, getListDebReports, reSendEmail } from '../../../service/debtReport';
import { ExportOutlined } from '@ant-design/icons';
import { useMutation } from '@tanstack/react-query';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import ModalHistoryDebtReport from './components/debtReportHistoryModal';

function ListDebtReports() {
  // const { modal } = App.useApp();
  const [selectedRows, setSelectedRows] = useState<IDebReport[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [isOpenModalAssignDebtReport, setIsOpenModalAssignDebtReport] = useState(false);
  const [isOpenModalDeleteAssigner, setIsOpenModalDeleteAssigner] = useState<boolean>(false);
  const [isOpenModalHistoryDebtReport, setIsOpenModalHistoryDebtReport] = useState(false);

  const [currentRecord, setCurrentRecord] = useState<IDebReport>();

  const {
    data: listCustomer,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<IDebReport[]>({
    queryKeyArrWithFilter: ['get-debt-reports'],
    api: getListDebReports,
  });

  const listData = listCustomer?.data?.data?.rows;

  const { mutateAsync: deleteDebtReport } = useDeleteField({
    apiQuery: deleteAssigner,
    keyOfListQuery: ['get-debt-reports'],
    messageSuccess: 'Gỡ phân bổ thành công',
  });

  const { mutateAsync: resendEmail } = useUpdateField({
    apiQuery: reSendEmail,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleDeleteDebtReportAssigner = React.useCallback(async () => {
    const res = await deleteDebtReport({ contractIds: [currentRecord?.id as string] });
    if (res?.data?.statusCode === '0') setIsOpenModalDeleteAssigner(false);
  }, [currentRecord?.id, deleteDebtReport]);

  const handleResendEmail = React.useCallback(
    async (record: IDebReport) => {
      try {
        await resendEmail({ customerCodes: [record?.customerCode as string] });
      } catch (error) {
        // console.log(error);
      }
    },
    [resendEmail],
  );

  const columnsTag: TableColumnsType = React.useMemo(() => {
    const newColumns = [...columns];
    newColumns.splice(6, 0, {
      title: 'Nhân viên công nợ',
      dataIndex: 'debtCollector',
      width: 200,
      key: 'debtCollector',
      render: (value: string, record) =>
        value ? (
          <Tag
            closable
            onClose={e => {
              e.preventDefault();
              setIsOpenModalDeleteAssigner(true);
              setCurrentRecord(record);
            }}
          >
            {value || 'Somename'}
          </Tag>
        ) : (
          <></>
        ),
    });
    const actionColumns: TableColumnsType = [
      ...newColumns,
      {
        dataIndex: 'action',
        key: 'action',
        width: '50px',
        align: 'center',
        fixed: 'right',
        render: (_: string, record: IDebReport) => {
          const actions = [
            {
              label: 'Gửi lại email',
              key: 'email',
              onClick: () => handleResendEmail(record),
            },
            {
              label: 'Lịch sử tương tác',
              key: 'history',
              onClick: () => {
                setCurrentRecord(record);
                setIsOpenModalHistoryDebtReport(!isOpenModalHistoryDebtReport);
              },
            },
          ];

          return <ActionsColumns overlayClassName="action-column-data-role" moreActions={actions} />;
        },
      },
    ];
    return actionColumns;
  }, [handleResendEmail, isOpenModalHistoryDebtReport]);

  const onSelectChange = (selectedRowKeys: Key[], selectedRows: IDebReport[]) => {
    setSelectedRows(selectedRows);
    setSelectedRowKeys(selectedRowKeys);
  };

  const rowSelection: TableRowSelection<IDebReport> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: Key[], selectedRows: IDebReport[]) => onSelectChange(selectedRowKeys, selectedRows),
  };

  const handleOpenModalAssignDebtReport = () => {
    setIsOpenModalAssignDebtReport(true);
  };
  const handleCancelModalAssignDebtReport = () => {
    setIsOpenModalAssignDebtReport(false);
  };

  const exportHistoryMutation = useMutation({
    mutationFn: () => exportDebtReport(),
  });

  const handleSubmitExport = async () => {
    try {
      const response = await exportHistoryMutation.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `CongNo.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  useEffect(() => {
    if (currentRecord && listCustomer?.data?.data?.rows?.length) {
      const updatedRecord = listCustomer?.data?.data?.rows?.find(item => item.id === currentRecord.id);
      if (updatedRecord) {
        setCurrentRecord(updatedRecord); // gán lại từ dữ liệu mới
      }
    }
  }, [currentRecord, listCustomer]);

  return (
    <div className="wrapper-list-personal-customers">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
          {selectedRowKeys.length > 0 && <Button onClick={handleOpenModalAssignDebtReport}>Phân bổ</Button>}
        </Flex>
        <Flex gap={10}>
          <Button type="default" onClick={handleSubmitExport}>
            <ExportOutlined />
            Xuất excel
          </Button>
        </Flex>
      </div>
      <div className="table-personal-customers">
        <TableComponent
          queryKeyArr={['get-debt-reports']}
          columns={columnsTag}
          rowSelection={rowSelection}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listData ?? []}
          rowKey="id"
        />
      </div>
      <ModalAssignDebtReport
        onClose={handleCancelModalAssignDebtReport}
        isOpen={isOpenModalAssignDebtReport}
        selectedRows={selectedRows}
      />
      <Modal
        className="modal-confirm-delete-assigner"
        open={isOpenModalDeleteAssigner}
        title={'Gỡ phân bổ'}
        centered
        closable={false}
        cancelText="Huỷ"
        destroyOnClose
        style={{ textAlign: 'center' }}
        footer={[
          <Button key="cancel" className="btn-cancel" type="text" onClick={() => setIsOpenModalDeleteAssigner(false)}>
            Huỷ
          </Button>,
          <Button key="confirm" className="btn-confirm" type="primary" onClick={handleDeleteDebtReportAssigner}>
            Xác nhận
          </Button>,
        ]}
      >
        Bạn có muốn gỡ nhân viên thu hồi công nợ này không?
      </Modal>

      <ModalHistoryDebtReport
        isOpen={isOpenModalHistoryDebtReport}
        record={currentRecord as IDebReport}
        handleCancel={() => setIsOpenModalHistoryDebtReport(!isOpenModalHistoryDebtReport)}
        queryKeyArr={['get-debt-reports']}
      />
    </div>
  );
}

export default ListDebtReports;
